import 'package:flutter/foundation.dart';
import '../models/audiobook.dart';
import '../models/playback_state.dart';
import '../services/audio_service.dart';

class AudiobookProvider extends ChangeNotifier {
  final AudioService _audioService = AudioService();
  
  List<Audiobook> _audiobooks = [];
  List<Audiobook> _filteredAudiobooks = [];
  String _searchQuery = '';
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<Audiobook> get audiobooks => _filteredAudiobooks;
  String get searchQuery => _searchQuery;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  PlaybackState get playbackState => _audioService.currentState;
  Audiobook? get currentAudiobook => _audioService.currentAudiobook;

  AudiobookProvider() {
    _audioService.initialize();
    _audioService.stateStream.listen((_) {
      notifyListeners();
    });
    _loadSampleAudiobooks();
  }

  void _loadSampleAudiobooks() {
    // Données d'exemple pour la démonstration
    _audiobooks = [
      Audiobook(
        id: '1',
        title: '<PERSON>',
        author: '<PERSON>',
        description: 'Un conte poétique et philosophique sous l\'apparence d\'un conte pour enfants.',
        coverImageUrl: 'https://via.placeholder.com/300x400/4CAF50/FFFFFF?text=Le+Petit+Prince',
        audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // URL d'exemple
        duration: const Duration(hours: 2, minutes: 30),
        chapters: [
          Chapter(
            id: 'ch1',
            title: 'Chapitre 1',
            startTime: Duration.zero,
            duration: const Duration(minutes: 15),
          ),
          Chapter(
            id: 'ch2',
            title: 'Chapitre 2',
            startTime: const Duration(minutes: 15),
            duration: const Duration(minutes: 20),
          ),
        ],
        genre: 'Littérature',
        rating: 4.8,
        publishedDate: DateTime(1943, 4, 6),
      ),
      Audiobook(
        id: '2',
        title: 'Les Misérables',
        author: 'Victor Hugo',
        description: 'Un roman historique français qui suit la vie de plusieurs personnages dans la France du XIXe siècle.',
        coverImageUrl: 'https://via.placeholder.com/300x400/2196F3/FFFFFF?text=Les+Miserables',
        audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // URL d'exemple
        duration: const Duration(hours: 48, minutes: 30),
        chapters: [
          Chapter(
            id: 'ch1',
            title: 'Tome I - Fantine',
            startTime: Duration.zero,
            duration: const Duration(hours: 12),
          ),
          Chapter(
            id: 'ch2',
            title: 'Tome II - Cosette',
            startTime: const Duration(hours: 12),
            duration: const Duration(hours: 10),
          ),
        ],
        genre: 'Classique',
        rating: 4.6,
        publishedDate: DateTime(1862, 3, 30),
      ),
      Audiobook(
        id: '3',
        title: 'L\'Étranger',
        author: 'Albert Camus',
        description: 'Un roman qui explore les thèmes de l\'absurdité de l\'existence humaine.',
        coverImageUrl: 'https://via.placeholder.com/300x400/FF9800/FFFFFF?text=L\'Etranger',
        audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // URL d'exemple
        duration: const Duration(hours: 4, minutes: 15),
        chapters: [
          Chapter(
            id: 'ch1',
            title: 'Première partie',
            startTime: Duration.zero,
            duration: const Duration(hours: 2),
          ),
          Chapter(
            id: 'ch2',
            title: 'Deuxième partie',
            startTime: const Duration(hours: 2),
            duration: const Duration(hours: 2, minutes: 15),
          ),
        ],
        genre: 'Philosophie',
        rating: 4.4,
        publishedDate: DateTime(1942, 5, 19),
      ),
    ];
    
    _filteredAudiobooks = List.from(_audiobooks);
    notifyListeners();
  }

  void searchAudiobooks(String query) {
    _searchQuery = query;
    if (query.isEmpty) {
      _filteredAudiobooks = List.from(_audiobooks);
    } else {
      _filteredAudiobooks = _audiobooks.where((audiobook) {
        return audiobook.title.toLowerCase().contains(query.toLowerCase()) ||
               audiobook.author.toLowerCase().contains(query.toLowerCase()) ||
               audiobook.genre.toLowerCase().contains(query.toLowerCase());
      }).toList();
    }
    notifyListeners();
  }

  void clearSearch() {
    searchAudiobooks('');
  }

  Future<void> playAudiobook(Audiobook audiobook) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await _audioService.loadAudiobook(audiobook);
      await _audioService.play();
    } catch (e) {
      _errorMessage = 'Erreur lors de la lecture: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> pausePlayback() async {
    await _audioService.pause();
  }

  Future<void> resumePlayback() async {
    await _audioService.play();
  }

  Future<void> stopPlayback() async {
    await _audioService.stop();
  }

  Future<void> seekTo(Duration position) async {
    await _audioService.seekTo(position);
  }

  Future<void> setPlaybackSpeed(double speed) async {
    await _audioService.setPlaybackSpeed(speed);
  }

  Future<void> skipForward([Duration duration = const Duration(seconds: 30)]) async {
    await _audioService.skipForward(duration);
  }

  Future<void> skipBackward([Duration duration = const Duration(seconds: 30)]) async {
    await _audioService.skipBackward(duration);
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  @override
  void dispose() {
    _audioService.dispose();
    super.dispose();
  }
}
