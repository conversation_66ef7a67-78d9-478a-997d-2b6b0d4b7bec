import 'dart:async';
import 'package:audioplayers/audioplayers.dart';
import '../models/audiobook.dart';
import '../models/playback_state.dart' as models;

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  final StreamController<models.PlaybackState> _stateController =
      StreamController<models.PlaybackState>.broadcast();

  models.PlaybackState _currentState = const models.PlaybackState();
  Audiobook? _currentAudiobook;

  Stream<models.PlaybackState> get stateStream => _stateController.stream;
  models.PlaybackState get currentState => _currentState;
  Audiobook? get currentAudiobook => _currentAudiobook;

  void initialize() {
    _audioPlayer.onPlayerStateChanged.listen((state) {
      models.PlayerState playerState;
      switch (state) {
        case PlayerState.playing:
          playerState = models.PlayerState.playing;
          break;
        case PlayerState.paused:
          playerState = models.PlayerState.paused;
          break;
        case PlayerState.stopped:
          playerState = models.PlayerState.stopped;
          break;
        case PlayerState.completed:
          playerState = models.PlayerState.stopped;
          break;
        default:
          playerState = models.PlayerState.stopped;
      }
      _updateState(_currentState.copyWith(playerState: playerState));
    });

    _audioPlayer.onPositionChanged.listen((position) {
      _updateState(_currentState.copyWith(currentPosition: position));
    });

    _audioPlayer.onDurationChanged.listen((duration) {
      _updateState(_currentState.copyWith(totalDuration: duration));
    });
  }

  Future<void> loadAudiobook(Audiobook audiobook) async {
    try {
      _currentAudiobook = audiobook;
      _updateState(_currentState.copyWith(
        currentAudiobookId: audiobook.id,
        playerState: models.PlayerState.loading,
        errorMessage: null,
      ));

      await _audioPlayer.setSource(UrlSource(audiobook.audioUrl));

      _updateState(_currentState.copyWith(
        playerState: models.PlayerState.stopped,
      ));
    } catch (e) {
      _updateState(_currentState.copyWith(
        playerState: models.PlayerState.error,
        errorMessage: 'Erreur lors du chargement: $e',
      ));
    }
  }

  Future<void> play() async {
    try {
      await _audioPlayer.resume();
    } catch (e) {
      _updateState(_currentState.copyWith(
        playerState: models.PlayerState.error,
        errorMessage: 'Erreur lors de la lecture: $e',
      ));
    }
  }

  Future<void> pause() async {
    try {
      await _audioPlayer.pause();
    } catch (e) {
      _updateState(_currentState.copyWith(
        playerState: models.PlayerState.error,
        errorMessage: 'Erreur lors de la pause: $e',
      ));
    }
  }

  Future<void> stop() async {
    try {
      await _audioPlayer.stop();
    } catch (e) {
      _updateState(_currentState.copyWith(
        playerState: models.PlayerState.error,
        errorMessage: 'Erreur lors de l\'arrêt: $e',
      ));
    }
  }

  Future<void> seekTo(Duration position) async {
    try {
      await _audioPlayer.seek(position);
    } catch (e) {
      _updateState(_currentState.copyWith(
        playerState: models.PlayerState.error,
        errorMessage: 'Erreur lors du déplacement: $e',
      ));
    }
  }

  Future<void> setPlaybackSpeed(double speed) async {
    try {
      await _audioPlayer.setPlaybackRate(speed);
      _updateState(_currentState.copyWith(playbackSpeed: speed));
    } catch (e) {
      _updateState(_currentState.copyWith(
        playerState: models.PlayerState.error,
        errorMessage: 'Erreur lors du changement de vitesse: $e',
      ));
    }
  }

  Future<void> skipForward(Duration duration) async {
    final newPosition = _currentState.currentPosition + duration;
    final maxPosition = _currentState.totalDuration;
    final targetPosition =
        newPosition > maxPosition ? maxPosition : newPosition;
    await seekTo(targetPosition);
  }

  Future<void> skipBackward(Duration duration) async {
    final newPosition = _currentState.currentPosition - duration;
    final targetPosition =
        newPosition < Duration.zero ? Duration.zero : newPosition;
    await seekTo(targetPosition);
  }

  void _updateState(models.PlaybackState newState) {
    _currentState = newState;
    _stateController.add(_currentState);
  }

  void dispose() {
    _audioPlayer.dispose();
    _stateController.close();
  }
}
