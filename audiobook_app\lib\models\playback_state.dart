enum PlayerState {
  stopped,
  playing,
  paused,
  loading,
  error,
}

class PlaybackState {
  final String? currentAudiobookId;
  final PlayerState playerState;
  final Duration currentPosition;
  final Duration totalDuration;
  final double playbackSpeed;
  final String? currentChapterId;
  final String? errorMessage;

  const PlaybackState({
    this.currentAudiobookId,
    this.playerState = PlayerState.stopped,
    this.currentPosition = Duration.zero,
    this.totalDuration = Duration.zero,
    this.playbackSpeed = 1.0,
    this.currentChapterId,
    this.errorMessage,
  });

  PlaybackState copyWith({
    String? currentAudiobookId,
    PlayerState? playerState,
    Duration? currentPosition,
    Duration? totalDuration,
    double? playbackSpeed,
    String? currentChapterId,
    String? errorMessage,
  }) {
    return PlaybackState(
      currentAudiobookId: currentAudiobookId ?? this.currentAudiobookId,
      playerState: playerState ?? this.playerState,
      currentPosition: currentPosition ?? this.currentPosition,
      totalDuration: totalDuration ?? this.totalDuration,
      playbackSpeed: playbackSpeed ?? this.playbackSpeed,
      currentChapterId: currentChapterId ?? this.currentChapterId,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  bool get isPlaying => playerState == PlayerState.playing;
  bool get isPaused => playerState == PlayerState.paused;
  bool get isLoading => playerState == PlayerState.loading;
  bool get hasError => playerState == PlayerState.error;
  bool get isStopped => playerState == PlayerState.stopped;

  double get progress {
    if (totalDuration.inMilliseconds == 0) return 0.0;
    return currentPosition.inMilliseconds / totalDuration.inMilliseconds;
  }

  String get formattedCurrentPosition {
    final hours = currentPosition.inHours;
    final minutes = currentPosition.inMinutes.remainder(60);
    final seconds = currentPosition.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String get formattedTotalDuration {
    final hours = totalDuration.inHours;
    final minutes = totalDuration.inMinutes.remainder(60);
    final seconds = totalDuration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  Duration get remainingTime => totalDuration - currentPosition;

  String get formattedRemainingTime {
    final remaining = remainingTime;
    final hours = remaining.inHours;
    final minutes = remaining.inMinutes.remainder(60);
    final seconds = remaining.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '-${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
    return '-${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
