import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/audiobook_provider.dart';
import '../widgets/audiobook_card.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/mini_player.dart';
import '../models/audiobook.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Livres Audio',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.library_books, color: Colors.white),
            onPressed: () {
              // TODO: Navigate to library
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings, color: Colors.white),
            onPressed: () {
              // TODO: Navigate to settings
            },
          ),
        ],
      ),
      body: Consumer<AudiobookProvider>(
        builder: (context, provider, child) {
          return Column(
            children: [
              // Search Bar
              SearchBarWidget(
                onChanged: provider.searchAudiobooks,
                onClear: provider.clearSearch,
                initialValue: provider.searchQuery,
              ),

              // Error Message
              if (provider.errorMessage != null)
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red[100],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red[300]!),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error, color: Colors.red[700]),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          provider.errorMessage!,
                          style: TextStyle(color: Colors.red[700]),
                        ),
                      ),
                      IconButton(
                        icon: Icon(Icons.close, color: Colors.red[700]),
                        onPressed: provider.clearError,
                      ),
                    ],
                  ),
                ),

              // Loading Indicator
              if (provider.isLoading)
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: CircularProgressIndicator(),
                ),

              // Audiobooks List
              Expanded(
                child: provider.audiobooks.isEmpty
                    ? _buildEmptyState(context, provider)
                    : ListView.builder(
                        itemCount: provider.audiobooks.length,
                        itemBuilder: (context, index) {
                          final audiobook = provider.audiobooks[index];
                          final isCurrentlyPlaying =
                              provider.currentAudiobook?.id == audiobook.id &&
                                  provider.playbackState.isPlaying;

                          return AudiobookCard(
                            audiobook: audiobook,
                            isCurrentlyPlaying: isCurrentlyPlaying,
                            onTap: () =>
                                _showAudiobookDetails(context, audiobook),
                            onPlayPressed: () =>
                                _handlePlayPress(context, provider, audiobook),
                          );
                        },
                      ),
              ),
            ],
          );
        },
      ),
      bottomNavigationBar: Consumer<AudiobookProvider>(
        builder: (context, provider, child) {
          final currentAudiobook = provider.currentAudiobook;
          final playbackState = provider.playbackState;

          if (currentAudiobook == null || playbackState.isStopped) {
            return const SizedBox.shrink();
          }

          return MiniPlayer(
            audiobook: currentAudiobook,
            playbackState: playbackState,
            onPlayPause: () {
              if (playbackState.isPlaying) {
                provider.pausePlayback();
              } else {
                provider.resumePlayback();
              }
            },
            onStop: provider.stopPlayback,
            onTap: () => _showAudiobookDetails(context, currentAudiobook),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, AudiobookProvider provider) {
    if (provider.searchQuery.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Aucun livre trouvé',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Essayez avec d\'autres mots-clés',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[500],
                  ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: provider.clearSearch,
              child: const Text('Effacer la recherche'),
            ),
          ],
        ),
      );
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.library_books,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Aucun livre audio disponible',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Ajoutez des livres à votre bibliothèque',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
          ),
        ],
      ),
    );
  }

  void _showAudiobookDetails(BuildContext context, Audiobook audiobook) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _AudiobookDetailsSheet(audiobook: audiobook),
    );
  }

  void _handlePlayPress(
      BuildContext context, AudiobookProvider provider, Audiobook audiobook) {
    final currentAudiobook = provider.currentAudiobook;
    final playbackState = provider.playbackState;

    if (currentAudiobook?.id == audiobook.id) {
      // Same audiobook - toggle play/pause
      if (playbackState.isPlaying) {
        provider.pausePlayback();
      } else {
        provider.resumePlayback();
      }
    } else {
      // Different audiobook - start playing
      provider.playAudiobook(audiobook);
    }
  }
}

class _AudiobookDetailsSheet extends StatelessWidget {
  final Audiobook audiobook;

  const _AudiobookDetailsSheet({required this.audiobook});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and Author
                  Text(
                    audiobook.title,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Par ${audiobook.author}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                  const SizedBox(height: 16),

                  // Description
                  Text(
                    'Description',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    audiobook.description,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 16),

                  // Details
                  _buildDetailRow(
                      context, 'Durée', audiobook.formattedDuration),
                  _buildDetailRow(context, 'Genre', audiobook.genre),
                  _buildDetailRow(context, 'Note', '${audiobook.rating}/5'),
                  _buildDetailRow(
                      context, 'Chapitres', '${audiobook.chapters.length}'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}
