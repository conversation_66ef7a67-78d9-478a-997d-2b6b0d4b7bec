class Audiobook {
  final String id;
  final String title;
  final String author;
  final String description;
  final String coverImageUrl;
  final String audioUrl;
  final Duration duration;
  final List<Chapter> chapters;
  final String genre;
  final double rating;
  final DateTime publishedDate;

  const Audiobook({
    required this.id,
    required this.title,
    required this.author,
    required this.description,
    required this.coverImageUrl,
    required this.audioUrl,
    required this.duration,
    required this.chapters,
    required this.genre,
    required this.rating,
    required this.publishedDate,
  });

  factory Audiobook.fromJson(Map<String, dynamic> json) {
    return Audiobook(
      id: json['id'] as String,
      title: json['title'] as String,
      author: json['author'] as String,
      description: json['description'] as String,
      coverImageUrl: json['coverImageUrl'] as String,
      audioUrl: json['audioUrl'] as String,
      duration: Duration(seconds: json['durationSeconds'] as int),
      chapters: (json['chapters'] as List<dynamic>)
          .map((chapter) => Chapter.fromJson(chapter as Map<String, dynamic>))
          .toList(),
      genre: json['genre'] as String,
      rating: (json['rating'] as num).toDouble(),
      publishedDate: DateTime.parse(json['publishedDate'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'author': author,
      'description': description,
      'coverImageUrl': coverImageUrl,
      'audioUrl': audioUrl,
      'durationSeconds': duration.inSeconds,
      'chapters': chapters.map((chapter) => chapter.toJson()).toList(),
      'genre': genre,
      'rating': rating,
      'publishedDate': publishedDate.toIso8601String(),
    };
  }

  String get formattedDuration {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    if (hours > 0) {
      return '${hours}h ${minutes}min';
    }
    return '${minutes}min';
  }
}

class Chapter {
  final String id;
  final String title;
  final Duration startTime;
  final Duration duration;

  const Chapter({
    required this.id,
    required this.title,
    required this.startTime,
    required this.duration,
  });

  factory Chapter.fromJson(Map<String, dynamic> json) {
    return Chapter(
      id: json['id'] as String,
      title: json['title'] as String,
      startTime: Duration(seconds: json['startTimeSeconds'] as int),
      duration: Duration(seconds: json['durationSeconds'] as int),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'startTimeSeconds': startTime.inSeconds,
      'durationSeconds': duration.inSeconds,
    };
  }

  Duration get endTime => startTime + duration;

  String get formattedStartTime {
    final hours = startTime.inHours;
    final minutes = startTime.inMinutes.remainder(60);
    final seconds = startTime.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
